# Mixamo Rig 批量导入功能使用说明

## 功能概述

批量导入功能允许您一次性处理文件夹中的多个FBX动画文件，自动将它们应用到您的Mixamo控制装备上。这大大提高了处理大量动画文件的效率。

## 主要特性

- **批量处理**：一次处理整个文件夹中的所有FBX文件
- **自动化流程**：自动导入、应用动画、清理临时对象
- **错误处理**：可选择跳过错误文件继续处理
- **进度反馈**：实时显示处理进度和结果
- **灵活配置**：可配置处理选项和限制

## 使用步骤

### 1. 准备工作

1. **创建控制装备**：
   - 导入您的Mixamo角色FBX文件
   - 选择骨架对象
   - 使用"Create Control Rig"创建控制装备

2. **准备动画文件**：
   - 将所有要处理的FBX动画文件放在同一个文件夹中
   - 确保文件名清晰易识别
   - 建议文件数量不要过多（建议50个以内）

### 2. 执行批量导入

1. **选择控制装备**：
   - 在3D视图中选择您的控制装备
   - 确保它是活动对象

2. **打开批量导入界面**：
   - 在3D视图侧边栏找到"Mixamo"面板
   - 展开"Animation"部分
   - 点击"批量导入FBX动画"按钮

3. **配置选项**：
   - **文件夹路径**：选择包含FBX文件的文件夹
   - **自动清理**：建议保持开启，自动清理临时对象
   - **跳过错误**：建议开启，遇到问题时继续处理其他文件
   - **最大文件数**：可限制处理的文件数量（0表示无限制）
   - **导入缩放**：FBX文件导入时的缩放倍数（默认10倍）

4. **开始处理**：
   - 点击"批量导入动画"开始处理
   - 观察控制台输出了解处理进度

### 3. 处理结果

- 每个成功处理的动画都会被保存到Blender的动作数据中
- 动画会根据文件名自动重命名（如：walk_forward, run_cycle等）
- 所有动画都保留，不会相互覆盖
- 导入的临时骨架会被自动删除
- 控制台会显示详细的处理进度和最终统计信息
- 处理完成后会显示所有导入动画的总结

## 配置选项详解

### 自动清理 (Auto Cleanup)
- **开启**：每处理完一个文件后自动删除导入的临时对象
- **关闭**：保留所有导入的对象（可能导致场景混乱）
- **建议**：保持开启

### 跳过错误 (Skip Errors)
- **开启**：遇到错误时跳过当前文件，继续处理下一个
- **关闭**：遇到错误时停止整个批量处理
- **建议**：开启，以确保能处理尽可能多的文件

### 最大文件数 (Max Files)
- **0**：处理文件夹中的所有FBX文件
- **>0**：限制处理的文件数量
- **建议**：首次使用时设置较小的数值进行测试

### 导入缩放 (Import Scale)
- **默认值**：10.0（十倍缩放）
- **范围**：0.1 - 100.0
- **用途**：调整导入的FBX文件的整体大小
- **说明**：Mixamo角色通常较小，10倍缩放可以匹配Blender的标准尺寸

## 注意事项

### 文件要求
- FBX文件必须包含骨架和动画数据
- 骨架结构应与Mixamo标准兼容
- 文件名应避免特殊字符

### 性能考虑
- 大量文件的处理需要较长时间
- 建议在处理前保存项目文件
- 处理过程中避免其他操作

### 错误处理
- 如果某个文件处理失败，检查文件是否损坏
- 确保文件路径中没有中文或特殊字符
- 查看控制台输出了解具体错误信息

## 工作流程建议

### 测试流程
1. 先用少量文件（2-3个）进行测试
2. 检查结果是否符合预期
3. 确认无误后再处理大批量文件

### 批量处理流程
1. 按动画类型分文件夹组织
2. 每次处理一个类型的动画
3. 及时保存重要的动画结果

### 备份策略
- 处理前备份原始项目文件
- 重要动画单独保存为独立文件
- 定期保存工作进度

## 故障排除

### 常见问题

**问题1：批量导入按钮不可用**
- 解决：确保选中了有效的控制装备

**问题2：找不到FBX文件**
- 解决：检查文件夹路径和文件扩展名

**问题3：处理过程中出错**
- 解决：开启"跳过错误"选项，查看控制台错误信息

**问题4：处理速度慢**
- 解决：减少同时处理的文件数量，关闭不必要的视图更新

### 调试方法
1. 查看Blender控制台输出
2. 检查系统控制台的详细错误信息
3. 逐个测试有问题的FBX文件

## 技术说明

### 处理流程
1. 扫描指定文件夹中的FBX文件
2. 逐个导入FBX文件到场景
3. 查找导入的骨架对象
4. 设置为源骨架并执行动画传输
5. 清理临时对象
6. 继续处理下一个文件

### 兼容性
- 支持Blender 2.80+
- 兼容标准Mixamo FBX格式
- 支持Windows、macOS、Linux

## 动画管理

### 访问导入的动画
1. **Dope Sheet编辑器**：
   - 切换到Action Editor模式
   - 在动作下拉列表中选择不同的动画
   - 直接播放和编辑选中的动画

2. **NLA编辑器**：
   - 将动画推送到NLA轨道
   - 混合多个动画创建复杂序列
   - 调整动画的时间和混合

3. **Python脚本**：
   ```python
   # 切换到指定动画
   target_rig.animation_data.action = bpy.data.actions['walk_forward']
   ```

### 动画命名规则
- 动画名称基于FBX文件名（不含扩展名）
- 如果名称重复，会自动添加数字后缀（如：walk_001, walk_002）
- 建议使用描述性的文件名（如：walk_forward.fbx, run_fast.fbx）

## 更新日志

### v1.1 - 工作流程优化
- ✅ 修复动画覆盖问题，所有动画都会被保存
- ✅ 自动删除导入的临时骨架对象
- ✅ 根据文件名自动重命名动画
- ✅ 添加10倍缩放支持（可配置）
- ✅ 改进错误处理和进度反馈
- ✅ 添加动画导入总结显示

### v1.0
- 初始版本
- 基本批量导入功能
- 错误处理和进度反馈
- 可配置的处理选项
