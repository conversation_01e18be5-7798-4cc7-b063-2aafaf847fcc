# Mixamo Rig 批量导入缩放功能更新

## 更新概述

为Mixamo Rig插件的批量导入功能添加了可配置的缩放选项，默认使用10倍缩放来匹配Blender的标准尺寸。

## 新增功能

### 🎯 导入缩放选项
- **默认值**: 10.0倍缩放
- **范围**: 0.1 - 100.0倍
- **精度**: 0.1倍增量
- **用途**: 自动调整导入FBX文件的整体大小

### 🔧 技术实现
- 在`MR_OT_batch_import_anim`类中添加`import_scale`属性
- 修改`_import_fbx_file`方法使用可配置的缩放值
- 更新FBX导入参数以包含缩放设置

## 为什么需要缩放？

### 📏 尺寸问题
- **Mixamo角色**: 默认高度约0.18米（过小）
- **Blender标准**: 人物高度约1.8米
- **解决方案**: 10倍缩放使角色达到合适尺寸

### 🎮 实际应用
- 便于与其他Blender资产配合使用
- 匹配标准的相机和灯光设置
- 符合游戏开发的常见尺寸要求

## 使用方法

### 1. 基本使用
1. 打开批量导入对话框
2. 选择FBX文件夹
3. 在"导入缩放"字段设置缩放倍数（默认10.0）
4. 开始批量处理

### 2. 常用缩放值
- **1.0倍**: Mixamo原始尺寸
- **10.0倍**: 推荐标准尺寸（默认）
- **0.5倍**: 较小角色
- **20.0倍**: 较大角色

### 3. 项目建议
- 在同一项目中保持一致的缩放倍数
- 根据场景需求调整缩放值
- 考虑目标平台的尺寸要求

## 技术细节

### 修改的文件
- `mixamo_rig_batch.py`: 添加缩放属性和实现
- `批量导入使用说明.md`: 更新文档
- `example_batch_usage.py`: 更新示例

### 新增属性
```python
import_scale: bpy.props.FloatProperty(
    name="导入缩放",
    description="FBX文件导入时的缩放倍数",
    default=10.0,
    min=0.1,
    max=100.0,
    step=1.0,
    precision=1
)
```

### FBX导入参数
```python
bpy.ops.import_scene.fbx(
    filepath=fbx_file,
    global_scale=self.import_scale,  # 可配置缩放
    use_custom_normals=True,
    use_image_search=True
)
```

## 兼容性

### ✅ 向后兼容
- 现有功能保持不变
- 默认10倍缩放适合大多数用途
- 用户可以根据需要调整

### 🔄 升级说明
- 无需额外配置
- 自动应用新的缩放功能
- 保持原有的工作流程

## 测试验证

### 测试脚本
- `test_scale_import.py`: 缩放功能测试
- `example_batch_usage.py`: 使用示例更新

### 验证步骤
1. 导入少量FBX文件测试
2. 比较不同缩放值的效果
3. 验证动画和绑定是否正常
4. 确认与其他资产的尺寸匹配

## 使用建议

### 🎯 最佳实践
1. **首次使用**: 用默认10倍缩放
2. **测试阶段**: 先用少量文件验证效果
3. **项目开发**: 保持一致的缩放设置
4. **团队协作**: 记录和共享缩放标准

### ⚠️ 注意事项
- 过大的缩放可能影响性能
- 过小的缩放可能导致精度问题
- 导出时考虑目标平台要求
- 与现有资产保持尺寸一致性

## 故障排除

### 常见问题
**Q: 角色太大或太小怎么办？**
A: 调整"导入缩放"值，建议范围1.0-20.0

**Q: 动画效果异常？**
A: 检查缩放值是否合理，避免极端数值

**Q: 与其他模型不匹配？**
A: 统一项目中所有资产的缩放标准

### 调试方法
1. 查看控制台的缩放信息输出
2. 比较导入前后的对象尺寸
3. 测试不同缩放值的效果

## 更新日志

### v1.1 - 缩放功能
- ✅ 添加可配置的导入缩放选项
- ✅ 默认10倍缩放匹配Blender标准
- ✅ 更新文档和示例
- ✅ 添加缩放信息输出
- ✅ 保持向后兼容性

### 下一步计划
- 考虑添加预设缩放值
- 支持不同文件的不同缩放
- 添加缩放预览功能

## 总结

这次更新解决了Mixamo角色在Blender中尺寸过小的问题，通过可配置的缩放功能，用户可以轻松调整导入的FBX文件大小，使其更好地适应Blender的工作环境和项目需求。默认的10倍缩放设置适合大多数用途，同时保持了灵活性以满足特殊需求。
