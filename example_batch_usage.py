"""
批量导入功能使用示例
这个脚本演示了如何通过代码调用批量导入功能
"""

import bpy
import os

def example_batch_import():
    """示例：通过代码调用批量导入功能"""
    
    print("=== 批量导入示例 ===")
    
    # 1. 检查当前是否有控制装备
    if not bpy.context.active_object:
        print("请先选择一个控制装备")
        return
    
    if bpy.context.active_object.type != "ARMATURE":
        print("请选择一个骨架对象")
        return
    
    if "mr_control_rig" not in bpy.context.active_object.data.keys():
        print("请选择一个Mixamo控制装备")
        return
    
    print(f"使用控制装备: {bpy.context.active_object.name}")
    
    # 2. 设置FBX文件夹路径（请修改为您的实际路径）
    fbx_folder = r"C:\path\to\your\fbx\animations"  # 修改这里
    
    if not os.path.exists(fbx_folder):
        print(f"文件夹不存在: {fbx_folder}")
        print("请修改脚本中的fbx_folder路径")
        return
    
    # 3. 检查文件夹中是否有FBX文件
    fbx_files = [f for f in os.listdir(fbx_folder) if f.lower().endswith('.fbx')]
    if not fbx_files:
        print(f"在文件夹中未找到FBX文件: {fbx_folder}")
        return
    
    print(f"找到 {len(fbx_files)} 个FBX文件")
    
    # 4. 调用批量导入操作
    try:
        # 方法1：直接调用操作（推荐使用UI界面）
        print("建议使用UI界面进行批量导入操作")
        print("在3D视图 > Mixamo面板 > Animation > 批量导入FBX动画")
        
        # 方法2：如果需要通过代码调用（高级用法）
        # bpy.ops.mr.batch_import_anim('INVOKE_DEFAULT', directory=fbx_folder)
        
    except Exception as e:
        print(f"调用批量导入时出错: {e}")

def setup_test_environment():
    """设置测试环境"""
    
    print("=== 设置测试环境 ===")
    
    # 清理场景
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # 添加一个立方体作为示例
    bpy.ops.mesh.primitive_cube_add()
    cube = bpy.context.active_object
    cube.name = "TestCube"
    
    print("测试环境已设置")
    print("注意：您需要导入一个Mixamo角色并创建控制装备才能使用批量导入功能")

def show_batch_import_info():
    """显示批量导入功能信息"""
    
    print("\n=== 批量导入功能信息 ===")
    
    # 检查操作是否可用
    try:
        op = getattr(bpy.ops.mr, 'batch_import_anim', None)
        if op:
            print("✓ 批量导入操作已注册")
            
            # 显示操作的文档字符串
            if hasattr(op, '__doc__') and op.__doc__:
                print(f"描述: {op.__doc__}")
            
        else:
            print("✗ 批量导入操作未找到")
            print("请确保插件已正确安装和启用")
            
    except Exception as e:
        print(f"检查批量导入操作时出错: {e}")
    
    # 显示相关的场景属性
    scene = bpy.context.scene
    if hasattr(scene, 'mix_source_armature'):
        print("✓ 源骨架属性已注册")
    else:
        print("✗ 源骨架属性未找到")

def create_sample_folder_structure():
    """创建示例文件夹结构"""
    
    print("\n=== 创建示例文件夹结构 ===")
    
    # 获取当前blend文件目录
    blend_file = bpy.data.filepath
    if not blend_file:
        print("请先保存当前文件")
        return None
    
    base_dir = os.path.dirname(blend_file)
    sample_dir = os.path.join(base_dir, "sample_animations")
    
    try:
        if not os.path.exists(sample_dir):
            os.makedirs(sample_dir)
            print(f"✓ 创建示例文件夹: {sample_dir}")
        else:
            print(f"✓ 示例文件夹已存在: {sample_dir}")
        
        # 创建子文件夹
        subfolders = ["walk_animations", "run_animations", "idle_animations"]
        for subfolder in subfolders:
            subfolder_path = os.path.join(sample_dir, subfolder)
            if not os.path.exists(subfolder_path):
                os.makedirs(subfolder_path)
                print(f"  ✓ 创建子文件夹: {subfolder}")
        
        # 创建说明文件
        readme_path = os.path.join(sample_dir, "README.txt")
        if not os.path.exists(readme_path):
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write("Mixamo动画文件夹\n")
                f.write("================\n\n")
                f.write("请将您的FBX动画文件放入相应的子文件夹中：\n")
                f.write("- walk_animations: 行走动画\n")
                f.write("- run_animations: 跑步动画\n")
                f.write("- idle_animations: 待机动画\n\n")
                f.write("使用批量导入功能时，选择对应的子文件夹即可。\n")
            print(f"  ✓ 创建说明文件: README.txt")
        
        return sample_dir
        
    except Exception as e:
        print(f"创建示例文件夹时出错: {e}")
        return None

def main():
    """主函数"""
    
    print("Mixamo Rig 批量导入功能示例")
    print("=" * 40)
    
    # 显示功能信息
    show_batch_import_info()
    
    # 创建示例文件夹
    sample_dir = create_sample_folder_structure()
    
    # 显示使用说明
    print("\n=== 使用说明 ===")
    print("1. 导入一个Mixamo角色FBX文件")
    print("2. 选择骨架并创建控制装备")
    print("3. 将FBX动画文件放入示例文件夹")
    if sample_dir:
        print(f"   示例文件夹: {sample_dir}")
    print("4. 使用UI界面的批量导入功能")
    print("5. 选择包含FBX文件的文件夹")
    print("6. 配置选项并开始批量处理")
    print("   - 导入缩放：默认10倍缩放，可根据需要调整")
    print("   - 自动清理：建议开启")
    print("   - 跳过错误：建议开启")

    print("\n=== 注意事项 ===")
    print("- 确保FBX文件包含兼容的骨架结构")
    print("- 默认使用10倍缩放匹配Blender标准尺寸")
    print("- 建议先用少量文件测试")
    print("- 处理前备份重要数据")
    print("- 查看控制台输出了解处理进度")

if __name__ == "__main__":
    main()
