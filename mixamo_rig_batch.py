import bpy
import os
from bpy.types import Operator, Panel
from bpy.props import StringProperty, BoolProperty, IntProperty
from bpy_extras.io_utils import ImportHelper
from .utils import *
from .define import *

# 导入主模块中的函数
from . import mixamo_rig


class MR_OT_batch_import_anim(Operator, ImportHelper):
    """批量导入文件夹中的FBX动画文件到控制装备"""
    
    bl_idname = "mr.batch_import_anim"
    bl_label = "批量导入动画"
    bl_options = {'UNDO'}
    
    # 文件夹选择属性
    directory: StringProperty(
        name="文件夹路径",
        description="选择包含FBX文件的文件夹",
        subtype='DIR_PATH'
    )
    
    # 文件过滤器
    filename_ext = ".fbx"
    filter_glob: StringProperty(
        default="*.fbx",
        options={'HIDDEN'}
    )
    
    # 批量处理选项
    auto_cleanup: BoolProperty(
        name="自动清理",
        description="处理完每个文件后自动清理临时对象",
        default=True
    )
    
    skip_errors: BoolProperty(
        name="跳过错误",
        description="遇到错误时跳过当前文件继续处理下一个",
        default=True
    )
    
    max_files: IntProperty(
        name="最大文件数",
        description="限制处理的文件数量（0表示无限制）",
        default=0,
        min=0
    )
    
    @classmethod
    def poll(cls, context):
        """检查是否可以执行批量导入"""
        if context.active_object:
            if context.active_object.type == "ARMATURE":
                if "mr_control_rig" in context.active_object.data.keys():
                    return True
        return False
    
    def execute(self, context):
        """执行批量导入"""
        scn = context.scene
        
        # 检查文件夹路径
        if not self.directory:
            self.report({'ERROR'}, "请选择包含FBX文件的文件夹")
            return {'CANCELLED'}
        
        if not os.path.exists(self.directory):
            self.report({'ERROR'}, "指定的文件夹不存在")
            return {'CANCELLED'}
        
        # 获取目标控制装备
        target_rig = context.active_object
        if not target_rig or target_rig.type != "ARMATURE":
            self.report({'ERROR'}, "请选择一个控制装备")
            return {'CANCELLED'}
        
        # 获取FBX文件列表
        fbx_files = self._get_fbx_files(self.directory)
        
        if not fbx_files:
            self.report({'WARNING'}, "在指定文件夹中未找到FBX文件")
            return {'CANCELLED'}
        
        # 限制文件数量
        if self.max_files > 0:
            fbx_files = fbx_files[:self.max_files]
        
        # 保存当前状态
        original_objects = set(bpy.data.objects.keys())
        processed_count = 0
        error_count = 0
        
        self.report({'INFO'}, f"开始批量处理 {len(fbx_files)} 个FBX文件...")
        
        # 批量处理每个FBX文件
        for i, fbx_file in enumerate(fbx_files):
            try:
                self.report({'INFO'}, f"处理文件 {i+1}/{len(fbx_files)}: {os.path.basename(fbx_file)}")
                
                # 处理单个FBX文件
                success = self._process_single_fbx(context, fbx_file, target_rig, original_objects)
                
                if success:
                    processed_count += 1
                else:
                    error_count += 1
                    if not self.skip_errors:
                        break
                        
            except Exception as e:
                error_count += 1
                error_msg = f"处理文件 {os.path.basename(fbx_file)} 时出错: {str(e)}"
                self.report({'ERROR'}, error_msg)
                print(error_msg)
                
                if not self.skip_errors:
                    break
        
        # 最终清理
        if self.auto_cleanup:
            self._final_cleanup()
        
        # 报告结果
        result_msg = f"批量处理完成！成功: {processed_count}, 错误: {error_count}"
        self.report({'INFO'}, result_msg)
        print(result_msg)
        
        return {'FINISHED'}
    
    def _get_fbx_files(self, directory):
        """获取文件夹中的所有FBX文件"""
        fbx_files = []
        try:
            for filename in os.listdir(directory):
                if filename.lower().endswith('.fbx'):
                    fbx_files.append(os.path.join(directory, filename))
            fbx_files.sort()  # 按文件名排序
        except Exception as e:
            print(f"读取文件夹时出错: {e}")
        
        return fbx_files
    
    def _process_single_fbx(self, context, fbx_file, target_rig, original_objects):
        """处理单个FBX文件"""
        try:
            # 1. 导入FBX文件
            imported_objects = self._import_fbx_file(fbx_file, original_objects)
            
            if not imported_objects:
                print(f"导入FBX文件失败: {fbx_file}")
                return False
            
            # 2. 查找导入的骨架对象
            source_armature = self._find_source_armature(imported_objects)
            
            if not source_armature:
                print(f"在导入的对象中未找到骨架: {fbx_file}")
                self._cleanup_imported_objects(imported_objects)
                return False
            
            # 3. 设置源骨架并执行动画导入
            success = self._import_animation_to_rig(context, source_armature, target_rig)
            
            # 4. 清理导入的对象
            if self.auto_cleanup:
                self._cleanup_imported_objects(imported_objects)
            
            return success
            
        except Exception as e:
            print(f"处理FBX文件时出错 {fbx_file}: {e}")
            return False
    
    def _import_fbx_file(self, fbx_file, original_objects):
        """导入FBX文件并返回新导入的对象"""
        try:
            # 导入FBX文件
            bpy.ops.import_scene.fbx(filepath=fbx_file)
            
            # 获取新导入的对象
            current_objects = set(bpy.data.objects.keys())
            new_objects = current_objects - original_objects
            
            imported_objects = [bpy.data.objects[name] for name in new_objects if name in bpy.data.objects]
            
            return imported_objects
            
        except Exception as e:
            print(f"导入FBX文件失败 {fbx_file}: {e}")
            return []
    
    def _find_source_armature(self, imported_objects):
        """在导入的对象中查找骨架对象"""
        for obj in imported_objects:
            if obj.type == 'ARMATURE':
                # 检查是否有动画数据
                if obj.animation_data and obj.animation_data.action:
                    return obj
        
        # 如果没有找到有动画的骨架，返回第一个骨架
        for obj in imported_objects:
            if obj.type == 'ARMATURE':
                return obj
        
        return None
    
    def _import_animation_to_rig(self, context, source_armature, target_rig):
        """将动画从源骨架导入到目标控制装备"""
        try:
            # 保存当前场景状态
            original_active = context.active_object
            layer_select = enable_all_armature_layers()
            
            # 设置源骨架
            context.scene.mix_source_armature = source_armature
            
            # 设置目标装备为活动对象
            bpy.ops.object.mode_set(mode='OBJECT')
            bpy.ops.object.select_all(action='DESELECT')
            set_active_object(target_rig.name)
            
            # 执行动画导入
            mixamo_rig._import_anim(source_armature, target_rig, import_only=True)
            
            # 恢复状态
            restore_armature_layers(layer_select)
            mixamo_rig.remove_retarget_cns(target_rig)

            if context.scene.mix_source_armature:
                try:
                    mixamo_rig.remove_retarget_cns(context.scene.mix_source_armature)
                except:
                    pass
            
            mixamo_rig.remove_temp_objects()
            
            return True
            
        except Exception as e:
            print(f"动画导入失败: {e}")
            return False
    
    def _cleanup_imported_objects(self, imported_objects):
        """清理导入的对象"""
        try:
            bpy.ops.object.mode_set(mode='OBJECT')
            bpy.ops.object.select_all(action='DESELECT')
            
            for obj in imported_objects:
                if obj and obj.name in bpy.data.objects:
                    obj.select_set(True)
            
            bpy.ops.object.delete()
            
        except Exception as e:
            print(f"清理导入对象时出错: {e}")
    
    def _final_cleanup(self):
        """最终清理"""
        try:
            mixamo_rig.remove_temp_objects()
            mixamo_rig.clean_scene()
        except Exception as e:
            print(f"最终清理时出错: {e}")


# 注册类列表
classes = (
    MR_OT_batch_import_anim,
)


def register():
    """注册批量处理模块"""
    from bpy.utils import register_class

    for cls in classes:
        register_class(cls)


def unregister():
    """注销批量处理模块"""
    from bpy.utils import unregister_class

    for cls in reversed(classes):
        unregister_class(cls)
