"""
测试批量导入功能的脚本
使用方法：
1. 在Blender中打开一个包含Mixamo控制装备的场景
2. 运行此脚本来测试批量导入功能
"""

import bpy
import os

def test_batch_import():
    """测试批量导入功能"""
    
    print("=== 开始测试批量导入功能 ===")
    
    # 检查是否有活动的控制装备
    if not bpy.context.active_object:
        print("错误：没有选中的对象")
        return False
    
    if bpy.context.active_object.type != "ARMATURE":
        print("错误：选中的对象不是骨架")
        return False
    
    if "mr_control_rig" not in bpy.context.active_object.data.keys():
        print("错误：选中的骨架不是Mixamo控制装备")
        return False
    
    print("✓ 找到有效的控制装备:", bpy.context.active_object.name)
    
    # 检查批量导入操作是否可用
    try:
        # 尝试获取批量导入操作
        op_class = getattr(bpy.ops.mr, 'batch_import_anim', None)
        if op_class is None:
            print("错误：批量导入操作未注册")
            return False
        
        print("✓ 批量导入操作已注册")
        
        # 检查poll函数
        if hasattr(op_class, 'poll'):
            can_execute = op_class.poll(bpy.context)
            if can_execute:
                print("✓ 批量导入操作可以执行")
            else:
                print("警告：批量导入操作当前不可执行")
        
        return True
        
    except Exception as e:
        print(f"错误：检查批量导入操作时出错: {e}")
        return False

def create_test_fbx_folder():
    """创建测试用的FBX文件夹结构"""
    
    # 获取当前blend文件的目录
    blend_filepath = bpy.data.filepath
    if not blend_filepath:
        print("警告：当前文件未保存，无法创建测试文件夹")
        return None
    
    blend_dir = os.path.dirname(blend_filepath)
    test_dir = os.path.join(blend_dir, "test_fbx_animations")
    
    if not os.path.exists(test_dir):
        try:
            os.makedirs(test_dir)
            print(f"✓ 创建测试文件夹: {test_dir}")
        except Exception as e:
            print(f"错误：创建测试文件夹失败: {e}")
            return None
    else:
        print(f"✓ 测试文件夹已存在: {test_dir}")
    
    return test_dir

def show_usage_instructions():
    """显示使用说明"""
    
    print("\n=== 批量导入功能使用说明 ===")
    print("1. 确保您有一个已经创建的Mixamo控制装备")
    print("2. 准备一个包含FBX动画文件的文件夹")
    print("3. 在3D视图的Mixamo面板中找到'Animation'部分")
    print("4. 点击'批量导入FBX动画'按钮")
    print("5. 选择包含FBX文件的文件夹")
    print("6. 配置批量处理选项：")
    print("   - 自动清理：处理完每个文件后自动清理临时对象")
    print("   - 跳过错误：遇到错误时跳过当前文件继续处理")
    print("   - 最大文件数：限制处理的文件数量")
    print("7. 点击'批量导入动画'开始处理")
    print("\n注意事项：")
    print("- 确保FBX文件包含与控制装备兼容的骨架结构")
    print("- 建议在处理前备份您的项目文件")
    print("- 大量文件的处理可能需要较长时间")

def main():
    """主测试函数"""
    
    # 测试基本功能
    success = test_batch_import()
    
    if success:
        print("\n✓ 批量导入功能测试通过")
        
        # 创建测试文件夹
        test_dir = create_test_fbx_folder()
        
        if test_dir:
            print(f"\n提示：您可以将FBX动画文件放入以下文件夹进行测试：")
            print(f"  {test_dir}")
        
        # 显示使用说明
        show_usage_instructions()
        
    else:
        print("\n✗ 批量导入功能测试失败")
        print("请检查插件是否正确安装和启用")

if __name__ == "__main__":
    main()
